<template>
	<view class="stock-info-container">
		<!-- 顶部导航 -->
		<view class="top-nav">
			<view class="nav-left">
				<uni-breadcrumb separator="/">
					<uni-breadcrumb-item v-for="(item,index) in warehouseNav" :key="index">{{item.name}}</uni-breadcrumb-item>
				</uni-breadcrumb>
			</view>
			<view class="nav-right" @click="openWarehouseDrawer">
				<uni-icons type="list" size="24"></uni-icons>
			</view>
		</view>
		
		<!-- 搜索区域 -->
		<view class="search-section">
			<view class="search-bar">
				<uv-search 
					shape="round" 
					v-model="searchParams.materialName" 
					placeholder="请输入物料名称" 
					clearable 
					bgColor="#f5f5f5"
					@search="handleSearch"
					@custom="handleSearch"
					@clear="handleClear"
				></uv-search>
			</view>
		</view>

		<!-- 库存状态Tab -->
		<view class="status-tabs">
			<scroll-view scroll-x class="tab-scroll">
				<view class="tab-container">
					<view 
						class="tab-item" 
						:class="{ 'active': !searchParams.status }"
						@click="selectStatus('')">
						<text>全部</text>
					</view>
					<view 
						v-for="item in statusOptions" 
						:key="item.value"
						class="tab-item"
						:class="{ 'active': searchParams.status === item.value }"
						@click="selectStatus(item.value)">
						<text>{{ item.label }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 库存列表 -->
		<scroll-view
			scroll-y="true"
			class="stock-list"
			@scrolltolower="loadMoreList"
			refresher-enabled
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onPullDownRefresh"
			enable-flex="true"
			@scroll="handleScroll"
			:scroll-top="scrollTop"
			:scroll-with-animation="isScrollWithAnimation"
			:scroll-animation-duration="animationDuration"
		>
					<!-- 有数据时显示列表 -->
		<block v-if="stockList && stockList.length > 0">
			<view class="list-container">
				<stock-info-list-item
					v-for="item in stockListWithClasses"
					:key="item.id"
					:item="item"
					:material-type-options="materialTypeOptions"
					:status-options="statusOptions"
					:unit-options="unitOptions"
					:dict-data="dictData"
					:is-expanded="expandedItemId === item.id"
					@toggle-expand="handleToggleExpand"
					@item-click="handleItemClick"
				/>
			</view>

			<!-- 加载更多 -->
			<view class="load-more-container">
				<uni-load-more :status="loadMoreStatus" @clickLoadMore="loadMoreList"></uni-load-more>
			</view>
		</block>
			
			<!-- 无数据时显示暂无数据提示 -->
			<view v-if="!stockList || stockList.length === 0" class="empty-data">
				<text>暂无库存数据</text>
			</view>
		</scroll-view>

		<!-- 返回顶部按钮 -->
		<view class="back-to-top" v-show="showBackToTop" @click="scrollToTop">
			<uni-icons type="top" size="24" color="#fff"></uni-icons>
		</view>

		<!-- 左侧仓库选择抽屉 -->
		<uni-popup ref="warehousePopup" type="left" background-color="#ffffff">
			<view class="popup-content">
				<view class="popup-title">
					<text>仓库选择</text>
					<uni-icons type="close" size="20" @click="closeWarehouseDrawer"></uni-icons>
				</view>
				
				<scroll-view scroll-y class="warehouse-tree">
					<!-- 全部仓库选项 -->
					<view class="warehouse-item level-0" 
						:class="{ 'active': !searchParams.warehouseName }"
						@click="selectWarehouse('', '全部仓库')">
						<text>全部仓库</text>
					</view>
					
					<!-- 树形仓库列表 -->
					<view v-for="warehouse in warehouseTree" :key="warehouse.id">
						<view class="warehouse-item level-0"
							:class="{ 'active': searchParams.warehouseName === warehouse.name }"
							@click="handleWarehouseClick(warehouse)">
							<text>{{ warehouse.name }}</text>
							<uni-icons v-if="warehouse.children && warehouse.children.length > 0" 
								:type="expandedWarehouse === warehouse.id ? 'bottom' : 'right'" 
								size="16"
								@click.stop="toggleWarehouse(warehouse.id)"></uni-icons>
						</view>
						
						<!-- 子仓库 -->
						<view v-if="expandedWarehouse === warehouse.id && warehouse.children && warehouse.children.length > 0" 
							class="sub-warehouses">
							<view v-for="subWarehouse in warehouse.children" 
								:key="subWarehouse.id" 
								class="warehouse-item level-1"
								:class="{ 'active': searchParams.warehouseName === subWarehouse.name }"
								@click="handleWarehouseClick(subWarehouse, warehouse)">
								<text>{{ subWarehouse.name }}</text>
								<text class="warehouse-desc">{{ subWarehouse.address || '' }}</text>
								<uni-icons v-if="subWarehouse.children && subWarehouse.children.length > 0" 
									:type="expandedWarehouse === subWarehouse.id ? 'bottom' : 'right'" 
									size="14"
									@click.stop="toggleWarehouse(subWarehouse.id)"></uni-icons>
							</view>
							
							<!-- 三级子仓库 -->
							<view v-for="subWarehouse in warehouse.children" :key="subWarehouse.id">
								<view v-if="expandedWarehouse === subWarehouse.id && subWarehouse.children && subWarehouse.children.length > 0" 
									class="sub-sub-warehouses">
									<view v-for="subSubWarehouse in subWarehouse.children" 
										:key="subSubWarehouse.id" 
										class="warehouse-item level-2"
										:class="{ 'active': searchParams.warehouseName === subSubWarehouse.name }"
										@click="handleWarehouseClick(subSubWarehouse, subWarehouse)">
										<text>{{ subSubWarehouse.name }}</text>
										<text class="warehouse-desc">{{ subSubWarehouse.address || '' }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import {
	getStockInfoPageApi,
	getUnitListApi,
	getWarehouseListApi
} from '../../../../../api/scm/inventory/stockInfo/index.js'
import { getBatchDictOptions, getDictLabel, DICT_TYPE } from '../../../../../utils/dict.js'
import { handleTree } from '../../../../../utils/tree.js'
import StockInfoListItem from './components/stockInfoListItem.vue'

export default {
	components: {
		StockInfoListItem
	},
	data() {
		return {
			stockList: [],
			pageNo: 1,
			pageSize: 10,
			loadMoreStatus: 'more',
			isRefreshing: false,
			
			// 搜索参数
			searchParams: {
				materialName: '',
				materialCode: '',
				materialType: '',
				warehouseName: '',
				status: '',
				detail: true
			},
			
			// 导航面包屑
			warehouseNav: [
				{
					name: '全部仓库'
				}
			],
			
			// 仓库树形展开状态
			expandedWarehouse: '',

			// 当前展开的库存项ID
			expandedItemId: null,
			
			// 选项数据
			materialTypeOptions: [],
			statusOptions: [],
			warehouseOptions: [],
			warehouseTree: [],
			unitOptions: [],
			unitMap: new Map(),
			
			// 字典数据
			dictData: {},

			// 返回顶部相关
			showBackToTop: false,
			scrollTop: 0,
			isScrollWithAnimation: false,
			animationDuration: 200, // 动画持续时间，单位毫秒，默认300ms，改为200ms更快
			currentScrollTop: 0
		}
	},
	computed: {
		warehouseText() {
			return this.searchParams.warehouseName
		},
		
		// 为每个库存项目计算类名
		stockListWithClasses() {
			return this.stockList.map(item => ({
				...item,
				materialTypeClass: this.getMaterialTypeClass(item.materialType),
				statusClass: this.getStatusClass(item.status)
			}))
		}
	},
	onLoad() {
		this.initData()
	},
	methods: {
		// 初始化数据
		async initData() {
			await this.getDictData()
			await this.getUnitList()
			await this.getWarehouseList()
			this.getStockList()
		},
		
		// 获取字典数据
		async getDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.MATERIAL_TYPE,
					DICT_TYPE.STOCK_STATUS,
					DICT_TYPE.MATERIAL_SOURCE,
					DICT_TYPE.SCM_BIZ_TYPE,
					DICT_TYPE.INVENTORY_MOVE_TYPE,
					DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION
				]
				this.dictData = await getBatchDictOptions(dictTypes)

				// 设置选项数据
				this.materialTypeOptions = this.dictData[DICT_TYPE.MATERIAL_TYPE] || []
				this.statusOptions = this.dictData[DICT_TYPE.STOCK_STATUS] || []

			} catch (error) {
				this.$modal.msgError('获取字典数据失败')
			}
		},
		
		// 获取库存列表
		async getStockList(isLoadMore = false) {
			if (!isLoadMore) {
				this.pageNo = 1
				this.stockList = []
			}
			
			this.loadMoreStatus = 'loading'
			
			try {
				const params = {
					...this.searchParams,
					pageNo: this.pageNo,
					pageSize: this.pageSize
				}
				
				const response = await getStockInfoPageApi(params)
				
				if (response && response.code === 0) {
					const newList = response.data?.list || []
					
					if (isLoadMore) {
						this.stockList = [...this.stockList, ...newList]
					} else {
						this.stockList = newList
					}
					
					this.loadMoreStatus = newList.length < this.pageSize ? 'noMore' : 'more'
				} else {
					this.loadMoreStatus = 'noMore'
					this.$modal.msgError('获取库存数据失败')
				}
			} catch (error) {
				this.loadMoreStatus = 'noMore'
				this.$modal.msgError('网络错误，请重试')
			} finally {
				if (this.isRefreshing) {
					this.isRefreshing = false
				}
			}
		},
		
		// 获取单位列表
		async getUnitList() {
			try {
				const response = await getUnitListApi({ pageSize: 100, pageNo: 1 })
				if (response && response.code === 0) {
					this.unitOptions = response.data?.list || []
					this.unitOptions.forEach(unit => {
						this.unitMap.set(unit.id, unit.name)
					})
				}
			} catch (error) {
				this.$modal.msgError('获取单位列表失败')
			}
		},
		
		// 获取仓库列表
		async getWarehouseList() {
			try {
				const response = await getWarehouseListApi({ pageNo: 1, pageSize: 100 })
				if (response && response.code === 0) {
					this.warehouseOptions = response.data || []
					// 使用handleTree处理成树形结构
					this.warehouseTree = handleTree(this.warehouseOptions, 'id', 'parentId', 'children')
				}
			} catch (error) {
				this.$modal.msgError('获取仓库列表失败')
			}
		},
		
		// 搜索处理
		handleSearch() {
			this.getStockList()
		},
		
		// 清空搜索
		handleClear() {
			this.searchParams.materialName = ''
			this.getStockList()
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.isRefreshing = true
			this.getStockList()
		},
		
		// 加载更多
		loadMoreList() {
			if (this.loadMoreStatus === 'loading' || this.loadMoreStatus === 'noMore') {
				return
			}
			this.pageNo++
			this.getStockList(true)
		},
		
		// 打开仓库选择抽屉
		openWarehouseDrawer() {
			this.$refs.warehousePopup.open()
		},
		
		// 关闭仓库选择抽屉
		closeWarehouseDrawer() {
			this.$refs.warehousePopup.close()
		},
		
		// 切换仓库展开状态
		toggleWarehouse(warehouseId) {
			this.expandedWarehouse = this.expandedWarehouse === warehouseId ? '' : warehouseId
		},
		
		// 处理仓库点击事件
		handleWarehouseClick(warehouse, parentWarehouse) {
			if (warehouse.children && warehouse.children.length > 0) {
				// 如果是父节点，则展开/收缩
				this.toggleWarehouse(warehouse.id)
			} else {
				// 如果是叶子节点，则选择
				this.selectWarehouse(warehouse.name, warehouse.name, parentWarehouse)
			}
		},
		
		// 选择仓库
		selectWarehouse(warehouseName, displayName, parentWarehouse) {
			this.searchParams.warehouseName = warehouseName
			this.updateWarehouseNav(displayName, parentWarehouse)
			this.closeWarehouseDrawer()
			this.getStockList()
		},
		
		// 更新仓库导航
		updateWarehouseNav(displayName, parentWarehouse) {
			if (displayName && displayName !== '全部仓库') {
				this.warehouseNav = [{ name: '全部仓库' }]
				if (parentWarehouse) {
					this.warehouseNav.push({ name: parentWarehouse.name })
				}
				this.warehouseNav.push({ name: displayName })
			} else {
				this.warehouseNav = [{ name: '全部仓库' }]
			}
		},
		
		// 选择状态
		selectStatus(value) {
			this.searchParams.status = value
			this.getStockList()
		},
		
		// 格式化数量
		formatQuantity(value) {
			if (!value && value !== 0) return '0'
			return Number(value).toLocaleString()
		},
		
		// 格式化金额
		formatAmount(value) {
			if (!value && value !== 0) return '¥0.00'
			return '¥' + Number(value).toFixed(2)
		},
		
		// 获取单位名称
		getUnitName(unitId) {
			if (!unitId) return ''
			return this.unitMap.get(unitId) || unitId.toString()
		},
		
		// 获取物料类型文本
		getMaterialTypeText(type) {
			return getDictLabel(this.materialTypeOptions, type) || type || '未知'
		},
		
		// 获取物料类型样式类
		getMaterialTypeClass(type) {
			const typeClassMap = {
				'RAW_MATERIAL': 'type-raw',
				'SEMI_FINISHED': 'type-semi', 
				'FINISHED_PRODUCT': 'type-finished',
				'AUXILIARY_MATERIAL': 'type-auxiliary'
			}
			return typeClassMap[type] || 'type-default'
		},
		
		// 获取物料来源文本
		getMaterialSourceText(source) {
			const materialSourceOptions = this.dictData[DICT_TYPE.MATERIAL_SOURCE] || []
			return getDictLabel(materialSourceOptions, source) || source || '未知'
		},
		
		// 获取状态文本
		getStatusText(status) {
			return getDictLabel(this.statusOptions, status) || status || '未知'
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			const statusClassMap = {
				'NORMAL': 'status-normal',
				'LOCKED': 'status-locked',
				'FROZEN': 'status-frozen',
				'DAMAGED': 'status-damaged'
			}
			return statusClassMap[status] || 'status-default'
		},

		// 滚动事件处理
		handleScroll(e) {
			this.currentScrollTop = e.detail.scrollTop;
			// 显示/隐藏返回顶部按钮
			this.showBackToTop = this.currentScrollTop > 300;
		},

		// 返回顶部
		scrollToTop() {
			this.isScrollWithAnimation = true;
			this.scrollTop = this.currentScrollTop; // 先设置当前位置
			this.$nextTick(() => {
				this.scrollTop = 0; // 然后滚动到顶部
				// 动画完成后重置状态
				setTimeout(() => {
					this.isScrollWithAnimation = false;
				}, this.animationDuration);
			});
		},

		// 处理库存项展开/收起
		handleToggleExpand(itemId) {
			// 如果点击的是当前展开的项，则收起
			if (this.expandedItemId === itemId) {
				this.expandedItemId = null;
			} else {
				// 否则展开新的项（同时收起之前展开的项）
				this.expandedItemId = itemId;
			}
		},

		// 处理库存项点击事件
		handleItemClick(item) {
			// 跳转到编辑页面

			// 检查是否有有效的ID
			if (!item || !item.id) {
				uni.showToast({
					title: '库存信息无效',
					icon: 'error',
					duration: 2000
				});
				return;
			}

			// 跳转到编辑页面，传递库存ID
			uni.navigateTo({
				url: `/pages/biz/scm/inventory/stockInfo/edit`,
				success: function(res) {
					res.eventChannel.emit('acceptDataFormOpener',{
						stockId:item.id
					})
				},
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.stock-info-container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.top-nav {
	border-bottom: 1px solid #eee;
	width: 100%;
	background-color: #fff;
	padding: 10px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.nav-left {
	flex: 1;
	overflow: hidden;
}

.nav-right {
	width: 40px;
	text-align: right;
}

.search-section {
	background-color: white;
	padding: 20rpx;
	border-bottom: 1px solid #eee;
}

.search-bar {
	margin-bottom: 0;
}

// 状态Tab样式
.status-tabs {
	background-color: white;
	border-bottom: 1px solid #eee;
}

.tab-scroll {
	white-space: nowrap;
}

.tab-container {
	display: flex;
	padding: 0 20rpx;
}

.tab-item {
	flex-shrink: 0;
	padding: 24rpx 32rpx;
	margin-right: 20rpx;
	text-align: center;
	border-bottom: 4rpx solid transparent;
	
	&.active {
		color: #2979ff;
		border-bottom-color: #2979ff;
		
		text {
			font-weight: bold;
		}
	}
	
	text {
		font-size: 28rpx;
		color: #666;
	}
	
	&.active text {
		color: #2979ff;
	}
}

.stock-list {
	flex: 1;
	padding: 20rpx;
}

.list-container {
	padding: 0;
}

.load-more-container {
	padding: 20rpx 0;
}

.empty-data {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

// 仓库选择抽屉样式
.popup-content {
	width: 80vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.popup-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px;
	border-bottom: 1px solid #eee;
	font-size: 32rpx;
	font-weight: bold;
}

.warehouse-tree {
	flex: 1;
	padding: 10px;
}

.warehouse-item {
	padding: 15px 10px;
	border-bottom: 1px solid #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: space-between;
	
	&.active {
		color: #2979ff;
		background-color: #f0f9ff;
	}
	
	&.level-1 {
		padding-left: 30px;
		background-color: #fafafa;
	}
	
	&.level-2 {
		padding-left: 50px;
		background-color: #f5f5f5;
	}
	
	.warehouse-desc {
		font-size: 24rpx;
		color: #999;
		margin-top: 8rpx;
		display: block;
	}
}

.sub-warehouses {
	.warehouse-item {
		border-left: 2px solid #e6e6e6;
	}
}

.sub-sub-warehouses {
	.warehouse-item {
		border-left: 4px solid #d0d0d0;
	}
}

/* 返回顶部按钮 */
.back-to-top {
	position: fixed;
	right: 20px;
	bottom: 90px;
	width: 50px;
	height: 50px;
	border-radius: 50%;
	background-color: rgba(41, 121, 255, 0.8);
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
	z-index: 99;
}
</style>
